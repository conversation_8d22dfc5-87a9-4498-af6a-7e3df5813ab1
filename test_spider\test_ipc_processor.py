#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPC数据处理器测试脚本
用于测试数据清洗功能，不涉及数据库操作
"""

import json
from ipc_spider import IPCDataProcessor, DatabaseConfig

def test_data_processing():
    """测试数据处理功能"""
    print("=" * 60)
    print("IPC数据处理测试")
    print("=" * 60)
    
    # 创建处理器
    db_config = DatabaseConfig()
    processor = IPCDataProcessor(db_config)
    
    # 测试HTML清理功能
    print("\n1. 测试HTML标签清理:")
    test_html = '<div>AGRICULTURE; FORESTRY; ANIMAL HUSBANDRY; HUNTING; TRAPPING; FISHING </div>'
    cleaned = processor.clean_html_tags(test_html)
    print(f"原始: {test_html}")
    print(f"清理后: {cleaned}")
    
    # 测试分类层级确定
    print("\n2. 测试分类层级确定:")
    test_codes = ["A", "A01", "A01B", "A01B001", "A01B001000"]
    
    for code in test_codes:
        levels = processor.determine_class_levels(code)
        print(f"代码: {code:10} -> {levels}")
    
    # 测试加载少量数据
    print("\n3. 测试数据加载 (前10条):")
    try:
        classifications = processor.load_ipc_data("ipc.json")
        
        print(f"总共加载: {len(classifications)} 条数据")
        print("\n前10条数据:")
        print("-" * 100)
        print(f"{'ID':<10} {'Level1':<6} {'Level2':<6} {'Level3':<6} {'Level4':<10} {'Description':<50}")
        print("-" * 100)
        
        for i, cls in enumerate(classifications[:10]):
            desc = cls.class_description_en[:47] + "..." if len(cls.class_description_en) > 50 else cls.class_description_en
            print(f"{cls.class_id:<10} {cls.class_level_1:<6} {cls.class_level_2:<6} {cls.class_level_3:<6} {cls.class_level_4:<10} {desc:<50}")
        
        # 统计各层级数量
        print("\n4. 层级统计:")
        level_stats = {}
        for cls in classifications:
            if cls.class_level_4:
                level = "Level 4"
            elif cls.class_level_3:
                level = "Level 3"
            elif cls.class_level_2:
                level = "Level 2"
            elif cls.class_level_1:
                level = "Level 1"
            else:
                level = "Unknown"
            
            level_stats[level] = level_stats.get(level, 0) + 1
        
        for level, count in sorted(level_stats.items()):
            print(f"  {level}: {count:4d} 条")
            
    except FileNotFoundError:
        print("❌ 找不到 ipc.json 文件")
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成!")

if __name__ == "__main__":
    test_data_processing()
