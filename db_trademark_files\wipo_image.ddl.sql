create table db_spider.wipo_mark_image_details_new
(
    id                  int(10) auto_increment comment '自增id'
        primary key,
    st13                varchar(30)  default ''                not null comment '品牌id唯一标识',
    name                varchar(255) default ''                not null comment '图形商标编号/名称',
    colour_indicator    varchar(255) default ''                not null comment '颜色标识符',
    colour_claimed      longtext                               null comment '颜色声明',
    classification_kind varchar(255) default ''                not null comment '分类体系类型',
    classification_code longtext                               null comment '分类代码',
    description         longtext                               null comment '描述',
    classes_id          varchar(30)  default ''                not null comment '分类id',
    version             varchar(10)  default ''                not null comment '版本号',
    version_id          varchar(30)  default ''                not null comment '版本id',
    create_time         datetime     default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time         datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uni_mark_id
        unique (st13, name) comment '品牌id和图片id联合唯一索引'
)
    comment 'wipo品牌商标图片明细表';

create index idx_st13
    on db_spider.wipo_mark_image_details_new (st13);

create index idx_utime
    on db_spider.wipo_mark_image_details_new (update_time);

