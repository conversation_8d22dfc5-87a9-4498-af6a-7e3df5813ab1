create table db_spider.wipo_events_new
(
    id          int(10) auto_increment comment '自增id'
        primary key,
    st13        varchar(30)  default ''                not null comment '商标id唯一标识',
    office_kind varchar(255) default ''                not null comment '事件类型（官方处理动作）',
    gbd_kind    varchar(255) default ''                not null comment '官方公报/公告数据类型',
    date        varchar(10)  default ''                not null comment '事件发生日期',
    create_time datetime     default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uniq_id
        unique (st13, office_kind, gbd_kind, date)
)
    comment 'wipo品牌事件时间线表';

