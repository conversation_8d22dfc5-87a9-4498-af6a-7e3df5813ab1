-- auto-generated definition
create table wipo_brand_info_new
(
    id                                   bigint auto_increment
        primary key,
    st13                                 varchar(50)   default ''                not null comment '详情id(唯一)',
    brand_name                           longtext                                null comment '品牌名称',
    status_date                          varchar(255)  default ''                not null comment '状态时间',
    application_number                   varchar(255)  default ''                not null comment '序列号',
    gbd_status                           varchar(255)  default ''                not null comment '官方状态',
    kind                                 varchar(255)  default ''                not null comment '标记种类',
    brand_type                           varchar(255)  default ''                not null comment '类型',
    filing_place                         varchar(255)  default ''                not null comment '备案地',
    mark_feature                         varchar(255)  default ''                not null comment '标记特征',
    designated_countries                 varchar(255)  default ''                not null comment '指定的国家',
    registration_office_code             varchar(255)  default ''                not null comment '注册办事处代码',
    office_status                        varchar(255)  default ''                not null comment '官方状态',
    expiry_date                          varchar(255)  default ''                not null comment '到期时间',
    logo                                 varchar(50)   default ''                not null comment 'logo图片id',
    logo_small                           longtext                                null comment '小logo图片存储路径',
    logo_big                             longtext                                null comment '大logo图片存储路径',
    word_mark_specification_text         longtext                                null comment '文字标记规范——文本',
    word_mark_specification_languagecode varchar(255)  default ''                not null comment '文字标记规范——语言',
    application_languagecode             varchar(255)  default ''                not null comment '申请语言',
    application_date                     varchar(255)  default ''                not null comment '申请日期',
    applicants_countrycode               longtext                                null comment '申请人国家代码',
    office                               varchar(100)  default ''                not null comment '商标注册办公室代码',
    collection                           varchar(255)  default ''                not null comment '商标收藏或系列标识',
    applicant                            longtext                                null comment '申请人信息',
    score                                float(20, 10) default 0.0000000000      not null comment '商标相关评分或重要性指标',
    nice_class                           longtext charset utf16                  null comment '尼斯分类',
    designation                          varchar(2000) default ''                not null comment '商标描述或指定使用',
    runid                                varchar(255)  default ''                not null,
    status                               varchar(255)  default ''                not null comment '商标当前状态',
    extra                                longtext                                null comment '额外信息或扩展数据id',
    logos                                longtext                                null comment '相似图片id',
    create_time                          datetime      default CURRENT_TIMESTAMP not null comment '更新时间',
    update_time                          datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '创建时间',
    constraint uniq_k
        unique (st13)
)
    comment 'wipo品牌基础信息表';

create index idx_create_time
    on wipo_brand_info_new (create_time);

create index idx_update_time
    on wipo_brand_info_new (update_time);