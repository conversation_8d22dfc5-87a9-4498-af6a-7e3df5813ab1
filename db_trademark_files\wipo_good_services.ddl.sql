create table db_spider.wipo_goods_services_classification_new
(
    id             int(10) auto_increment comment '自增id'
        primary key,
    st13           varchar(30) default ''                not null comment '商标id唯一标识',
    kind           varchar(20) default ''                not null comment '分类体系类型',
    class_code     varchar(10) default ''                not null comment '尼斯分类号',
    description_fr longtext                              null comment '法语语言',
    description_en longtext                              null comment '英语语言',
    description_ar longtext                              null comment '阿拉伯语言',
    description_zh longtext                              null comment '中文语言',
    description_th longtext                              null comment '泰语',
    description_de longtext                              null comment '德语',
    description_es longtext                              null comment '西班牙语',
    description_ko longtext                              null comment '韩语',
    description_vi longtext                              null comment '越南语',
    description_ja longtext                              null comment '日语',
    description_uk longtext                              null comment '乌克兰语',
    description_it longtext                              null comment '意大利语',
    description_in longtext                              null comment '印度尼西亚语',
    description_he longtext                              null comment '希伯来语',
    description_pt longtext                              null comment '葡萄牙语',
    description_ro longtext                              null comment '罗马尼亚语',
    create_time    datetime    default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uni_skc_id
        unique (st13, kind, class_code)
)
    comment 'wipo品牌尼斯分类表';

create index idx_utime
    on db_spider.wipo_goods_services_classification_new (update_time);

