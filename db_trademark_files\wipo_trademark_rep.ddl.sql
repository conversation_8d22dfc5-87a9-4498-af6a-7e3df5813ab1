create table db_spider.wipo_representatives_new
(
    id           int(10) auto_increment comment '自增id'
        primary key,
    st13         varchar(30)  default ''                not null comment '品牌id唯一标识',
    identifier   varchar(50)  default ''                not null comment '代理人id',
    kind         varchar(255) default ''                not null comment '分类',
    full_name    longtext                               null comment '全名',
    full_address longtext                               null comment '地址',
    country_code varchar(10)  default ''                not null comment '国家代码',
    create_time  datetime     default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uni_mark_id
        unique (st13, identifier)
)
    comment 'wipo品牌代理人表';

create index idx_st13
    on db_spider.wipo_representatives_new (st13);

create index idx_utime
    on db_spider.wipo_representatives_new (update_time);

