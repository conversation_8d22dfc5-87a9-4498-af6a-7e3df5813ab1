-- auto-generated definition
create table wipo_class_map_new
(
    id             int auto_increment comment '自增id'
        primary key,
    code           varchar(50)   default ''                not null comment '分类代码',
    kind           varchar(50)   default ''                not null comment '类别',
    version        varchar(10)   default ''                not null comment '版本号',
    description_fr varchar(1000) default ''                not null comment '法语',
    description_en varchar(1000) default ''                not null comment '英语',
    description_ar varchar(1000) default ''                not null comment '阿拉伯语',
    description_zh varchar(1000) default ''                not null comment '中文',
    description_th varchar(1000) default ''                not null comment '泰语',
    description_de varchar(1000) default ''                not null comment '德语',
    description_es varchar(1000) default ''                not null comment '西班牙语',
    description_pt varchar(1000) default ''                not null comment '葡萄牙语',
    description_he varchar(1000) default ''                not null comment '希伯来语',
    description_vi varchar(1000) default ''                not null comment '越南语',
    description_in varchar(1000) default ''                not null comment '印度尼西亚语',
    description_ro varchar(1000) default ''                not null comment '罗马尼亚语',
    description_ko varchar(1000) default ''                not null comment '韩语',
    description_it varchar(1000) default ''                not null comment '意大利语',
    description_uk varchar(1000) default ''                not null comment '乌克兰语',
    description_ja varchar(1000) default ''                not null comment '日语',
    create_time    datetime      default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time    datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uniq_ckv
        unique (code, kind, version)
)
    comment 'wipo品牌配置表';

create index idx_utime
    on wipo_class_map_new (update_time);