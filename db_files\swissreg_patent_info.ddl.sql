-- auto-generated definition
create table swissreg_patent_info
(
    id                         bigint unsigned auto_increment
        primary key,
    open_id                    varchar(20)                  default ''                not null comment '数据源id',
    application_number         varchar(50)                  default ''                not null comment '专利应用ID',
    patent_id                  varchar(255)                 default ''                not null comment '专利ID',
    application_date           varchar(255)                 default ''                not null comment '专利应用日期',
    application_language       varchar(255)                 default ''                not null comment '专利应用语言',
    status                     varchar(255)                 default ''                not null comment '专利状态',
    publication_date           varchar(255)                 default ''                not null comment '专利出版日期',
    an_inheritance             tinyint(1) unsigned zerofill default 0                 not null comment '专利遗传以及传统标识，2：No，1：Yes',
    ip_type                    varchar(255)                 default ''                not null comment '专利知识产权',
    grant_date                 varchar(255)                 default ''                not null comment '专利授予日期',
    next_renewal               varchar(255)                 default ''                not null comment '下次续费日期',
    protection_date_max        varchar(255)                 default ''                not null comment '最长保护日期',
    International_registration varchar(255)                 default ''                not null comment '国际注册',
    Cancellation               varchar(255)                 default ''                not null comment '取消预订',
    patent_specifications      longtext                                               null comment '专利规格',
    international_publication  varchar(255)                 default ''                not null comment '国际出版',
    priorities                 longtext                                               null comment '优先事项',
    base_patent                varchar(255)                 default ''                not null comment '基础专利申请号',
    exhibition_immunity        varchar(255)                 default ''                not null comment '展览豁免权',
    paediatric_SPC             varchar(255)                 default ''                not null comment 'SPC',
    create_time                datetime                     default CURRENT_TIMESTAMP not null comment '创建日期',
    update_time                datetime                     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新日期',
    constraint uni_mark_id
        unique (open_id)
)
    comment 'swissreg专利详情表';

create index idx_create_time
    on swissreg_patent_info (create_time);

create index idx_utime
    on swissreg_patent_info (update_time);

