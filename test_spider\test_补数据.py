#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试补数据工具
快速测试单个分类的数据获取和处理
"""

from ipc_补数据工具 import IPCSupplementTool
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_single_fetch():
    """测试单个分类数据获取"""
    print("=" * 60)
    print("测试补数据工具 - 单个分类")
    print("=" * 60)
    
    tool = IPCSupplementTool()
    
    # 测试E05F分类（之前失败的）
    test_id = "E05F"
    
    print(f"\n测试获取 {test_id} 的第四级数据...")
    
    # 1. 仅获取数据，不入库
    level4_data = tool.fetch_level4_data(test_id)
    
    if level4_data:
        print(f"✅ 成功获取数据，共 {len(level4_data)} 条原始记录")
        
        # 2. 处理数据
        classifications = []
        for node in level4_data:
            if not isinstance(node, dict):
                continue
                
            classification = tool.process_level4_node(node)
            if classification:
                classifications.append(classification)
        
        print(f"✅ 处理完成，提取到 {len(classifications)} 条有效分类")
        
        # 3. 显示前几条数据
        print(f"\n{test_id} 分类示例:")
        print("-" * 100)
        print(f"{'Class ID':<20} {'SymbolCode':<20} {'Level1':<6} {'Level2':<6} {'Level3':<6} {'Level4':<20} {'Description':<30}")
        print("-" * 100)
        
        for i, cls in enumerate(classifications[:5]):
            desc = cls.class_description_en[:27] + "..." if len(cls.class_description_en) > 30 else cls.class_description_en
            # 从原始数据中获取symbolcode
            original_node = level4_data[i] if i < len(level4_data) else {}
            symbolcode = original_node.get('symbolcode', '')[:20]
            print(f"{cls.class_id:<20} {symbolcode:<20} {cls.class_level_1:<6} {cls.class_level_2:<6} {cls.class_level_3:<6} {cls.class_level_4:<20} {desc:<30}")
        
        if len(classifications) > 5:
            print(f"... 还有 {len(classifications) - 5} 条记录")
        
        # 4. 询问是否入库
        response = input(f"\n是否将 {test_id} 的数据写入数据库? (y/N): ").strip().lower()
        if response == 'y':
            print(f"\n开始入库 {test_id} 数据...")
            success = tool.supplement_single_classification(test_id)
            if success:
                print(f"✅ {test_id} 数据入库成功!")
            else:
                print(f"❌ {test_id} 数据入库失败!")
        else:
            print("跳过入库操作")
    
    else:
        print(f"❌ 获取 {test_id} 数据失败")


def test_multiple_fetch():
    """测试多个分类数据获取"""
    print("=" * 60)
    print("测试补数据工具 - 多个分类")
    print("=" * 60)
    
    tool = IPCSupplementTool()
    
    # 测试几个可能失败的分类
    test_ids = ["E05F", "A01C", "A01G"]
    
    print(f"测试分类: {test_ids}")
    
    # 仅测试获取，不入库
    for test_id in test_ids:
        print(f"\n--- 测试 {test_id} ---")
        level4_data = tool.fetch_level4_data(test_id, retry_count=2)
        
        if level4_data:
            # 处理数据
            classifications = []
            for node in level4_data:
                classification = tool.process_level4_node(node)
                if classification:
                    classifications.append(classification)
            
            print(f"✅ {test_id}: {len(level4_data)} 条原始 → {len(classifications)} 条有效")
        else:
            print(f"❌ {test_id}: 获取失败")
    
    # 询问是否批量入库
    response = input(f"\n是否批量入库所有成功获取的数据? (y/N): ").strip().lower()
    if response == 'y':
        print("\n开始批量入库...")
        results = tool.supplement_multiple_classifications(test_ids, delay=1.0)
        
        print("\n入库结果:")
        for test_id, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {test_id}: {status}")


def main():
    """主函数"""
    print("请选择测试模式:")
    print("1. 测试单个分类 (E05F)")
    print("2. 测试多个分类")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        test_single_fetch()
    elif choice == "2":
        test_multiple_fetch()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
