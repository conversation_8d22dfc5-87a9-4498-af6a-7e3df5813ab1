#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第四级数据获取功能
"""

import json
from ipc_spider import IPCDataProcessor, DatabaseConfig

def test_level4_fetch():
    """测试第四级数据获取功能"""
    print("=" * 60)
    print("第四级数据获取测试")
    print("=" * 60)
    
    # 创建处理器
    db_config = DatabaseConfig()
    processor = IPCDataProcessor(db_config)
    
    # 1. 测试从本地A01P.json文件加载数据
    print("\n1. 测试本地A01P.json文件处理:")
    try:
        with open("A01P.json", 'r', encoding='utf-8') as f:
            a01p_data = json.load(f)
        
        # 处理A01P数据
        a01p_classifications = processor.process_level4_data(a01p_data)
        
        print(f"A01P.json 包含 {len(a01p_data)} 条原始记录")
        print(f"提取到 {len(a01p_classifications)} 条有效分类")
        
        print("\nA01P分类示例:")
        print("-" * 100)
        print(f"{'Class ID':<15} {'SymbolCode':<15} {'Level1':<6} {'Level2':<6} {'Level3':<6} {'Level4':<15} {'Description':<30}")
        print("-" * 100)

        for i, cls in enumerate(a01p_classifications[:5]):
            desc = cls.class_description_en[:27] + "..." if len(cls.class_description_en) > 30 else cls.class_description_en
            # 从原始数据中获取symbolcode用于对比
            original_node = a01p_data[i] if i < len(a01p_data) else {}
            symbolcode = original_node.get('symbolcode', '')[:15]
            print(f"{cls.class_id:<15} {symbolcode:<15} {cls.class_level_1:<6} {cls.class_level_2:<6} {cls.class_level_3:<6} {cls.class_level_4:<15} {desc:<30}")
        
        if len(a01p_classifications) > 5:
            print(f"... 还有 {len(a01p_classifications) - 5} 条记录")
            
    except FileNotFoundError:
        print("❌ 找不到 A01P.json 文件")
    except Exception as e:
        print(f"❌ 处理A01P.json时出错: {e}")
    
    # 2. 测试从ipc.json提取第三级分类ID
    print("\n2. 测试提取第三级分类ID:")
    try:
        with open("ipc.json", 'r', encoding='utf-8') as f:
            ipc_data = json.load(f)
        
        level3_ids = processor.get_level3_classifications(ipc_data)
        print(f"发现 {len(level3_ids)} 个第三级分类")
        print("前10个第三级分类:", sorted(list(level3_ids))[:10])
        
    except FileNotFoundError:
        print("❌ 找不到 ipc.json 文件")
    except Exception as e:
        print(f"❌ 处理ipc.json时出错: {e}")
    
    # 3. 测试单个第四级数据获取 (使用A01P作为示例)
    print("\n3. 测试在线获取第四级数据 (A01P):")
    try:
        level4_data = processor.fetch_level4_data("A01P")
        
        if level4_data:
            level4_classifications = processor.process_level4_data(level4_data)
            print(f"✅ 成功获取A01P第四级数据: {len(level4_classifications)} 条分类")
            
            # 显示前几条
            print("\n在线获取的A01P分类示例:")
            print("-" * 80)
            for i, cls in enumerate(level4_classifications[:3]):
                desc = cls.class_description_en[:50] + "..." if len(cls.class_description_en) > 50 else cls.class_description_en
                print(f"  {cls.class_id}: {desc}")
        else:
            print("❌ 未能获取到A01P第四级数据")
            
    except Exception as e:
        print(f"❌ 在线获取数据时出错: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成!")

if __name__ == "__main__":
    test_level4_fetch()
