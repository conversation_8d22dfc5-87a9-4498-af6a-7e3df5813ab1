-- auto-generated definition
create table swissreg_patent_history
(
    id                   bigint auto_increment
        primary key,
    open_id              varchar(20)  default ''                not null comment '数据源id',
    publication_id       varchar(50)  default ''                not null comment '公开号',
    publication_text     longtext                               null comment '发布说明',
    publication_doc      varchar(255) default ''                not null comment '发布文档',
    publication_mark     longtext                               null comment '发布备注',
    aenderungsgrund      varchar(255) default ''                not null comment '变更原因',
    geaenderte_daten_alt longtext                               null comment '旧变更数据',
    geaenderte_daten_neu longtext                               null comment '新变更数据',
    image_refs           varchar(255) default ''                not null comment '图片引用',
    schutztitel_ref      varchar(255) default ''                not null comment '保护标题引用',
    aenderungsdatum      varchar(255)                           not null comment '变更日期',
    publication_sdatum   varchar(255) default ''                not null comment '发布日期',
    create_time          datetime     default CURRENT_TIMESTAMP not null comment '创建日期',
    update_time          datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新日期',
    constraint uni_mark_id
        unique (publication_id) comment '数据源id作为唯一索引'
)
    comment 'Swissreg 专利历史详情表';

