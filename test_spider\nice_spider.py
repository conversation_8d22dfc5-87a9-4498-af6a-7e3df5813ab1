import requests

cookies = {
    '_ce.s': 'lcw~1753258240547~v11.cs~411929~v11.s~85b44b70-679c-11f0-890c-81404e604462~v11.vs~f86d066d819c40001230b889f669729922c1f6c2~v11.fsvd~eyJ1cmwiOiJ3aXBvLmludC96aC93ZWIvcGF0ZW50c2NvcGUiLCJyZWYiOiJodHRwczovL3d3dy53aXBvLmludC96aC93ZWIvY2xhc3NpZmljYXRpb25zIiwidXRtIjpbXX0%3D~v11.sla~1753258240547~v11ls~85b44b70-679c-11f0-890c-81404e604462~lcw~1753258240583',
}

headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-TW,zh-CN;q=0.9,zh;q=0.8',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Pragma': 'no-cache',
    'Referer': 'https://patentscope.wipo.int/search/zh/search.jsf',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}
session = requests.Session()
response = session.get('https://patentscope.wipo.int/search/zh/search.jsf', cookies=cookies, headers=headers)

jsessionid=response.headers['Set-Cookie'].split(';')[0].split('=')[1]



# 2
cookies = {
    'JSESSIONID': jsessionid,
    '_ce.s': 'lcw~1753258240547~v11.cs~411929~v11.s~85b44b70-679c-11f0-890c-81404e604462~v11.vs~f86d066d819c40001230b889f669729922c1f6c2~v11.fsvd~eyJ1cmwiOiJ3aXBvLmludC96aC93ZWIvcGF0ZW50c2NvcGUiLCJyZWYiOiJodHRwczovL3d3dy53aXBvLmludC96aC93ZWIvY2xhc3NpZmljYXRpb25zIiwidXRtIjpbXX0%3D~v11.sla~1753258240547~v11ls~85b44b70-679c-11f0-890c-81404e604462~lcw~1753258240583',
    '_pk_id.5.ec75': '35af34c9def18ab7.1753258249.',
    '_pk_ses.5.ec75': '1',
    '_pk_uid': '0%3DMzVhZjM0YzlkZWYxOGFiNw%3D%3D',
}

headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-TW,zh-CN;q=0.9,zh;q=0.8',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Pragma': 'no-cache',
    'Referer': 'https://patentscope.wipo.int/search/zh/search.jsf',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}

response = session.get('https://patentscope.wipo.int/search/zh/search.jsf', cookies=cookies, headers=headers)


import re

ViewState = re.search(
    r'<input[^>]+name="javax\.faces\.ViewState"[^>]+value="(.*?)"', 
    response.text
).group(1)

print(ViewState)

# 3
cookies = {
    'JSESSIONID': jsessionid,
    '_ce.s': 'lcw~1753258240547~v11.cs~411929~v11.s~85b44b70-679c-11f0-890c-81404e604462~v11.vs~f86d066d819c40001230b889f669729922c1f6c2~v11.fsvd~eyJ1cmwiOiJ3aXBvLmludC96aC93ZWIvcGF0ZW50c2NvcGUiLCJyZWYiOiJodHRwczovL3d3dy53aXBvLmludC96aC93ZWIvY2xhc3NpZmljYXRpb25zIiwidXRtIjpbXX0%3D~v11.sla~1753258240547~v11ls~85b44b70-679c-11f0-890c-81404e604462~lcw~1753258240583',
    '_pk_id.5.ec75': '35af34c9def18ab7.1753258249.',
    '_pk_ses.5.ec75': '1',
    '_pk_uid': '0%3DMzVhZjM0YzlkZWYxOGFiNw%3D%3D',
}

headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-TW,zh-CN;q=0.9,zh;q=0.8',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/x-www-form-urlencoded',
    'Origin': 'https://patentscope.wipo.int',
    'Pragma': 'no-cache',
    'Referer': 'https://patentscope.wipo.int/search/zh/search.jsf',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}

data = {
    'simpleSearchForm': 'simpleSearchForm',
    'simpleSearchForm:field:input': 'FP',
    'simpleSearchForm:fpSearch:input': 'aaa',
    'simpleSearchForm:fpSearch:j_idt1331': '',
    'javax.faces.ViewState': ViewState,
}

response = session.post('https://patentscope.wipo.int/search/zh/search.jsf', cookies=cookies, headers=headers, data=data)
if response.status_code == 302:
    print(response.headers.get('location'))

elif response.status_code == 200:
    button_id = re.search(r'<button id="simpleSearchForm:fpSearch:(j_idt\d+)"', response.text).group(1)
    button_field = f'simpleSearchForm:fpSearch:{button_id}'
    data = {
    'simpleSearchForm': 'simpleSearchForm',
    'simpleSearchForm:field:input': 'FP',
    'simpleSearchForm:fpSearch:input': 'aaa',
    button_field: '',
    'javax.faces.ViewState': ViewState,
}

    response = session.post('https://patentscope.wipo.int/search/zh/search.jsf', cookies=cookies, headers=headers, data=data)
    print(response.url)
    with open('test.html', 'w', encoding='utf-8') as f:
        f.write(response.text)

# print(response.status_code)
# print(response.headers)


