create table wipo_correspondence_new
(
    id           int(10) auto_increment comment '自增id'
        primary key,
    st13         varchar(50) default ''                not null comment '品牌id',
    full_address longtext                              null comment '通信地址',
    country_code varchar(20)                           not null comment '国家代码',
    create_time  datetime    default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uni_mark_id
        unique (st13)
)
    comment 'wipo品牌通信地址表';

create index idx_utime
    on wipo_correspondence_new (update_time);