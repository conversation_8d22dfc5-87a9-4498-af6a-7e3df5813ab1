create table swissreg_patent_registered
(
    id                bigint auto_increment
        primary key,
    open_id           varchar(20)                            not null comment '数据源id',
    owner_id          varchar(50)  default ''                not null comment '所有者ID',
    owner_type        varchar(50)  default ''                not null,
    name              varchar(255) default ''                not null comment '所有者名称',
    additional_name   varchar(255) default ''                not null comment '附加名称',
    street            varchar(255) default ''                not null comment '街道',
    additional_street varchar(255) default ''                not null comment '附加街道',
    house_number      varchar(255) default ''                not null comment '门牌号',
    zip               varchar(255) default ''                not null comment '邮政编码',
    town              varchar(255) default ''                not null comment '城市',
    country           varchar(255) default ''                not null comment '国家',
    title             varchar(255) default ''                not null comment '职称',
    email             varchar(255) default ''                not null comment '电子邮件',
    phone_number      varchar(255) default ''                not null comment '电话号码',
    fax_number        varchar(255) default ''                not null comment '传真号码',
    language          varchar(255) default ''                not null comment '语言',
    modification      varchar(255) default ''                not null comment '修改信息',
    create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建日期',
    update_time       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新日期',
    constraint uni_mark_id
        unique (owner_id) comment '数据源id作为唯一索引'
)
    comment 'Swissreg 专利注册地址详情表';