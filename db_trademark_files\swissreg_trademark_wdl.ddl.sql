-- auto-generated definition
create table swissreg_trademark_vienna_classes
(
    id                  bigint auto_increment
        primary key,
    open_id             varchar(50)  default ''                not null comment '数据源ID',
    trademark_id        varchar(50)  default ''                not null comment '商标ID',
    vienna_class_number varchar(255) default ''                not null comment '维也纳分类号码',
    create_time         datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time         datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uni_mark_id
        unique (trademark_id, vienna_class_number) comment '商标ID唯一索引'
)
    comment '商标维也纳分类表';

create index idx_open_id
    on swissreg_trademark_vienna_classes (open_id);

create index idx_utime
    on swissreg_trademark_vienna_classes (update_time);

