create table db_spider.wipo_applicants_new
(
    id           int(10) auto_increment comment '自增id'
        primary key,
    st13         varchar(50) default ''                not null comment '品牌id（唯一索引）',
    identifier   varchar(50) default ''                not null comment '申请人身份id',
    kind         varchar(20) default ''                not null comment '申请人类别',
    full_name    longtext                              null comment '姓名信息',
    full_address longtext                              null comment '住址信息',
    country_code varchar(20) default ''                not null comment '国家代码',
    create_time  datetime    default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uni_mark_id
        unique (st13, identifier)
)
    comment 'wipo品牌申请人表';

create index idx_st13
    on db_spider.wipo_applicants_new (st13);

create index idx_utime
    on db_spider.wipo_applicants_new (update_time);

