#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPC分类第四级数据补数据工具
用于补充请求失败的第三级分类数据
"""

import json
import re
import pymysql
import requests
import time
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
import logging
from db_config import DATABASE_CONFIG, IPC_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class IPCClassification:
    """IPC分类数据结构"""
    class_id: str
    class_description_en: str
    class_type: str = "IPC"
    class_level_1: str = ""
    class_level_2: str = ""
    class_level_3: str = ""
    class_level_4: str = ""
    version: str = "2026.01"


class IPCSupplementTool:
    """IPC数据补充工具"""
    
    def __init__(self):
        self.db_config = DATABASE_CONFIG
        
    def clean_html_tags(self, text: str) -> str:
        """清理HTML标签，提取纯文本"""
        if not text:
            return ""
        
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', text)
        # 移除多余的空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        return clean_text
    
    def determine_class_levels(self, symbol_code: str) -> Dict[str, str]:
        """根据symbolcode确定分类层级"""
        levels = {
            "class_level_1": "",
            "class_level_2": "",
            "class_level_3": "",
            "class_level_4": ""
        }
        
        if not symbol_code:
            return levels
            
        # Section级别 (如: A)
        if len(symbol_code) == 1 and symbol_code.isalpha():
            levels["class_level_1"] = symbol_code
            
        # Class级别 (如: A01)
        elif len(symbol_code) == 3 and symbol_code[0].isalpha() and symbol_code[1:].isdigit():
            levels["class_level_1"] = symbol_code[0]
            levels["class_level_2"] = symbol_code
            
        # Subclass级别 (如: A01B)
        elif len(symbol_code) == 4 and symbol_code[0].isalpha() and symbol_code[1:3].isdigit() and symbol_code[3].isalpha():
            levels["class_level_1"] = symbol_code[0]
            levels["class_level_2"] = symbol_code[:3]
            levels["class_level_3"] = symbol_code
            
        # Main Group或更细分级别
        elif len(symbol_code) > 4:
            if symbol_code[0].isalpha() and len(symbol_code) >= 4:
                levels["class_level_1"] = symbol_code[0]
                if len(symbol_code) >= 3:
                    levels["class_level_2"] = symbol_code[:3]
                if len(symbol_code) >= 4:
                    levels["class_level_3"] = symbol_code[:4]
                if len(symbol_code) > 4:
                    levels["class_level_4"] = symbol_code
                    
        return levels
    
    def fetch_level4_data(self, level3_id: str, retry_count: int = 3) -> Optional[List[Dict]]:
        """获取第四级数据，带重试机制"""
        base_url = IPC_CONFIG['level4_base_url']
        url = f"{base_url}{level3_id}.json"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        
        for attempt in range(retry_count):
            try:
                logger.info(f"正在请求 {level3_id} 数据 (尝试 {attempt + 1}/{retry_count}): {url}")
                
                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                logger.info(f"✅ 成功获取 {level3_id} 数据，共 {len(data)} 条记录")
                
                return data
                
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ 请求失败 {level3_id} (尝试 {attempt + 1}/{retry_count}): {e}")
                if attempt < retry_count - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"❌ {level3_id} 所有重试均失败")
                    
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析失败 {level3_id}: {e}")
                break
                
        return None
    
    def process_level4_node(self, node: Dict) -> Optional[IPCClassification]:
        """处理第四级数据节点"""
        symbol_code = node.get('symbolcode', '').strip()
        if not symbol_code:
            return None
            
        # 获取描述信息
        title1 = node.get('title1', '')
        description = self.clean_html_tags(title1)
        
        if not description:
            return None
        
        # 使用symbol字段作为class_id
        symbol = node.get('symbol', '').strip()
        actual_class_id = symbol if symbol else symbol_code
            
        # 确定分类层级
        levels = self.determine_class_levels(symbol_code)
        
        # 创建分类对象
        classification = IPCClassification(
            class_id=actual_class_id,
            class_description_en=description,
            class_level_1=levels["class_level_1"],
            class_level_2=levels["class_level_2"],
            class_level_3=levels["class_level_3"],
            class_level_4=levels["class_level_4"]
        )
        
        return classification
    
    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(
            host=self.db_config["host"],
            port=self.db_config["port"],
            user=self.db_config["user"],
            password=self.db_config["password"],
            database=self.db_config["database"],
            charset=self.db_config["charset"],
            autocommit=False
        )
    
    def insert_classification(self, conn, classification: IPCClassification) -> bool:
        """插入单条分类数据"""
        sql = """
        INSERT INTO patent_class_map (
            class_type, class_id, class_level_1, class_level_2, 
            class_level_3, class_level_4, class_description_en, 
            class_description_cn, version
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) ON DUPLICATE KEY UPDATE
            class_level_1 = VALUES(class_level_1),
            class_level_2 = VALUES(class_level_2),
            class_level_3 = VALUES(class_level_3),
            class_level_4 = VALUES(class_level_4),
            class_description_en = VALUES(class_description_en),
            update_time = CURRENT_TIMESTAMP
        """
        
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql, (
                    classification.class_type,
                    classification.class_id,
                    classification.class_level_1,
                    classification.class_level_2,
                    classification.class_level_3,
                    classification.class_level_4,
                    classification.class_description_en,
                    "",  # class_description_cn 暂时为空
                    classification.version
                ))
            return True
        except Exception as e:
            logger.error(f"插入分类数据失败 {classification.class_id}: {e}")
            return False
    
    def supplement_single_classification(self, level3_id: str) -> bool:
        """补充单个第三级分类的第四级数据"""
        logger.info(f"开始补充 {level3_id} 的第四级数据...")
        
        # 1. 获取数据
        level4_data = self.fetch_level4_data(level3_id)
        if not level4_data:
            return False
        
        # 2. 处理数据
        classifications = []
        for node in level4_data:
            if not isinstance(node, dict):
                continue
                
            classification = self.process_level4_node(node)
            if classification:
                classifications.append(classification)
        
        if not classifications:
            logger.warning(f"{level3_id} 没有有效的第四级分类数据")
            return False
        
        # 3. 入库
        conn = None
        try:
            conn = self.get_db_connection()
            conn.begin()
            
            success_count = 0
            for classification in classifications:
                if self.insert_classification(conn, classification):
                    success_count += 1
            
            conn.commit()
            logger.info(f"✅ {level3_id} 补充完成，成功入库 {success_count}/{len(classifications)} 条数据")
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"❌ {level3_id} 入库失败: {e}")
            return False
        finally:
            if conn:
                conn.close()
    
    def supplement_multiple_classifications(self, level3_ids: List[str], delay: float = 2.0) -> Dict[str, bool]:
        """批量补充多个第三级分类的数据"""
        results = {}
        total = len(level3_ids)
        
        logger.info(f"开始批量补充 {total} 个分类的第四级数据...")
        
        for i, level3_id in enumerate(level3_ids, 1):
            logger.info(f"进度: {i}/{total} - 处理 {level3_id}")
            
            success = self.supplement_single_classification(level3_id)
            results[level3_id] = success
            
            # 添加延迟
            if i < total:
                time.sleep(delay)
        
        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量补充完成: 成功 {success_count}/{total}")
        
        return results


def main():
    """主函数"""
    print("=" * 60)
    print("IPC分类第四级数据补数据工具")
    print("=" * 60)
    
    tool = IPCSupplementTool()
    
    print("请选择补数据方式:")
    print("1. 补充单个分类")
    print("2. 补充多个分类")
    print("3. 从失败列表文件补充")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        level3_id = input("请输入第三级分类ID (如 E05F): ").strip().upper()
        if level3_id:
            tool.supplement_single_classification(level3_id)
    
    elif choice == "2":
        ids_input = input("请输入第三级分类ID列表 (用逗号分隔，如 E05F,A01C): ").strip()
        if ids_input:
            level3_ids = [id.strip().upper() for id in ids_input.split(',') if id.strip()]
            if level3_ids:
                tool.supplement_multiple_classifications(level3_ids)
    
    elif choice == "3":
        # 常见的可能失败的分类列表
        failed_ids = ["E05F", "A01C", "A01G"]  # 可以根据实际情况修改
        print(f"将补充以下分类: {failed_ids}")
        confirm = input("确认继续? (y/N): ").strip().lower()
        if confirm == 'y':
            tool.supplement_multiple_classifications(failed_ids)
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
