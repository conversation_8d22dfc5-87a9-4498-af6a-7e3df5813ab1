-- auto-generated definition
create table swissreg_patent_ipc
(
    id          bigint auto_increment
        primary key,
    open_id     varchar(20)                        not null comment '数据源id',
    ipc_id      varchar(255)                       not null comment 'ipc_id',
    ipc_date    varchar(255)                       not null comment 'ipc时间',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建日期',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新日期',
    constraint uni_mark_id
        unique (id)
)
    comment 'swissreg_ipc详情表';

