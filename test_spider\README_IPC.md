# IPC分类数据清洗和入库工具

## 功能说明

这个工具用于处理IPC（国际专利分类）JSON数据，将其清洗并写入到MySQL数据库中。

**主要功能：**
- 处理本地IPC基础数据 (1-3级分类)
- 自动获取第四级详细分类数据 (通过API请求)
- HTML标签清理和数据标准化
- 批量数据库入库操作

## 文件说明

- `ipc_spider.py` - 主要的数据处理和入库脚本
- `db_config.py` - 数据库连接配置文件
- `test_ipc_processor.py` - 基础数据处理测试脚本
- `test_level4_fetch.py` - 第四级数据获取测试脚本
- `ipc.json` - IPC分类原始数据文件 (1-3级)
- `A01P.json` - 第四级数据示例文件
- `patent_class_map.ddl.sql` - 数据库表结构定义

## 数据处理规则

### 字段映射
- `symbolcode` → `class_id` (分类ID)
- `title1` → `class_description_en` (分类描述，清理HTML标签)
- `class_type` = "IPC" (固定值)
- `version` = "2026.01" (固定值)

### 分类层级规则
根据 `symbolcode` 的格式确定分类层级：

- **Level 1 (Section)**: 1位字母，如 "A"
- **Level 2 (Class)**: 1位字母+2位数字，如 "A01"  
- **Level 3 (Subclass)**: 1位字母+2位数字+1位字母，如 "A01B"
- **Level 4 (Group)**: 更长的编码，如 "A01B001"

## 使用步骤

### 1. 配置数据库连接

编辑 `db_config.py` 文件，修改数据库连接参数：

```python
DATABASE_CONFIG = {
    "host": "your_host",           # 数据库主机
    "port": 3306,                  # 数据库端口
    "user": "your_username",       # 数据库用户名
    "password": "your_password",   # 数据库密码
    "database": "your_database",   # 数据库名称
    "charset": "utf8mb4"
}
```

### 2. 安装依赖

```bash
pip install pymysql
```

### 3. 安装额外依赖

第四级数据获取需要网络请求功能：

```bash
pip install requests
```

### 4. 测试数据处理（可选）

运行测试脚本验证数据处理功能：

```bash
# 测试基础数据处理
python test_ipc_processor.py

# 测试第四级数据获取
python test_level4_fetch.py
```

### 5. 执行数据入库

```bash
python ipc_spider.py
```

程序会：
1. 加载和处理IPC基础数据 (1-3级)
2. 询问是否获取第四级详细数据
3. 如果选择获取，会自动请求所有第三级分类的第四级数据
4. 显示最终数据统计信息
5. 询问是否清空现有数据
6. 批量插入数据到数据库

**注意：** 获取第四级数据需要网络连接，会向WIPO官方API发送请求。

## 数据库表结构

```sql
CREATE TABLE patent_class_map (
    id                   bigint auto_increment primary key,
    class_type           varchar(10)  not null comment '分类类型',
    class_id             varchar(10)  not null comment '分类ID',
    class_level_1        varchar(10)  not null comment '分类层级1',
    class_level_2        varchar(10)  not null comment '分类层级2',
    class_level_3        varchar(10)  not null comment '分类层级3',
    class_level_4        varchar(10)  not null comment '分类层级4',
    class_description_en varchar(255) not null comment '分类描述(en)',
    class_description_cn varchar(255) not null comment '分类描述(cn)',
    version              int          not null comment '分类版本',
    create_time          datetime default CURRENT_TIMESTAMP,
    update_time          datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
    unique key uni_class_id (class_id, version)
);
```

## 配置选项

在 `db_config.py` 中可以配置：

- `version`: IPC版本号（默认: "2026.01"）
- `batch_size`: 批量插入大小（默认: 100）
- `json_file`: JSON数据文件路径（默认: "ipc.json"）
- `level4_base_url`: 第四级数据API基础URL
- `request_delay`: 请求间隔时间（默认: 1.0秒）

## 注意事项

1. 确保数据库表已创建
2. 确保数据库用户有相应的读写权限
3. 大量数据插入时建议调整 `batch_size` 参数
4. 程序支持重复运行，会自动处理重复数据（ON DUPLICATE KEY UPDATE）
5. 第四级数据获取需要网络连接，请确保能访问WIPO官方API
6. 建议在网络稳定的环境下运行，避免请求失败
7. 第四级数据获取可能需要较长时间（656个分类 × 1秒延迟 ≈ 11分钟）

## 错误处理

- 程序会记录详细的日志信息
- 批量插入使用事务，失败时会自动回滚
- 支持断点续传（通过重复运行实现）
