# IPC分类数据清洗和入库工具

## 功能说明

这个工具用于处理IPC（国际专利分类）JSON数据，将其清洗并写入到MySQL数据库中。

## 文件说明

- `ipc_spider.py` - 主要的数据处理和入库脚本
- `db_config.py` - 数据库连接配置文件
- `test_ipc_processor.py` - 测试脚本，用于验证数据处理功能
- `ipc.json` - IPC分类原始数据文件
- `patent_class_map.ddl.sql` - 数据库表结构定义

## 数据处理规则

### 字段映射
- `symbolcode` → `class_id` (分类ID)
- `title1` → `class_description_en` (分类描述，清理HTML标签)
- `class_type` = "IPC" (固定值)
- `version` = "2026.01" (固定值)

### 分类层级规则
根据 `symbolcode` 的格式确定分类层级：

- **Level 1 (Section)**: 1位字母，如 "A"
- **Level 2 (Class)**: 1位字母+2位数字，如 "A01"  
- **Level 3 (Subclass)**: 1位字母+2位数字+1位字母，如 "A01B"
- **Level 4 (Group)**: 更长的编码，如 "A01B001"

## 使用步骤

### 1. 配置数据库连接

编辑 `db_config.py` 文件，修改数据库连接参数：

```python
DATABASE_CONFIG = {
    "host": "your_host",           # 数据库主机
    "port": 3306,                  # 数据库端口
    "user": "your_username",       # 数据库用户名
    "password": "your_password",   # 数据库密码
    "database": "your_database",   # 数据库名称
    "charset": "utf8mb4"
}
```

### 2. 安装依赖

```bash
pip install pymysql
```

### 3. 测试数据处理（可选）

运行测试脚本验证数据处理功能：

```bash
python test_ipc_processor.py
```

### 4. 执行数据入库

```bash
python ipc_spider.py
```

程序会：
1. 加载和处理IPC数据
2. 显示数据统计信息
3. 询问是否清空现有数据
4. 批量插入数据到数据库

## 数据库表结构

```sql
CREATE TABLE patent_class_map (
    id                   bigint auto_increment primary key,
    class_type           varchar(10)  not null comment '分类类型',
    class_id             varchar(10)  not null comment '分类ID',
    class_level_1        varchar(10)  not null comment '分类层级1',
    class_level_2        varchar(10)  not null comment '分类层级2',
    class_level_3        varchar(10)  not null comment '分类层级3',
    class_level_4        varchar(10)  not null comment '分类层级4',
    class_description_en varchar(255) not null comment '分类描述(en)',
    class_description_cn varchar(255) not null comment '分类描述(cn)',
    version              int          not null comment '分类版本',
    create_time          datetime default CURRENT_TIMESTAMP,
    update_time          datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
    unique key uni_class_id (class_id, version)
);
```

## 配置选项

在 `db_config.py` 中可以配置：

- `version`: IPC版本号（默认: "2026.01"）
- `batch_size`: 批量插入大小（默认: 100）
- `json_file`: JSON数据文件路径（默认: "ipc.json"）

## 注意事项

1. 确保数据库表已创建
2. 确保数据库用户有相应的读写权限
3. 大量数据插入时建议调整 `batch_size` 参数
4. 程序支持重复运行，会自动处理重复数据（ON DUPLICATE KEY UPDATE）

## 错误处理

- 程序会记录详细的日志信息
- 批量插入使用事务，失败时会自动回滚
- 支持断点续传（通过重复运行实现）
