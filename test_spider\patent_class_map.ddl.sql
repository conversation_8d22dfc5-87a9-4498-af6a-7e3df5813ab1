-- auto-generated definition
create table patent_class_map
(
    id                   bigint auto_increment
        primary key,
    class_type           varchar(10)                        not null comment '分类类型',
    class_id             varchar(10)                        not null comment '分类ID',
    class_level_1        varchar(10)                        not null comment '分类层级1',
    class_level_2        varchar(10)                        not null comment '分类层级2',
    class_level_3        varchar(10)                        not null comment '分类层级3',
    class_level_4        varchar(10)                        not null comment '分类层级4',
    class_description_en varchar(255)                       not null comment '分类描述(en)',
    class_description_cn varchar(255)                       not null comment '分类描述(cn)',
    version              int                                not null comment '分类版本',
    create_time          datetime default CURRENT_TIMESTAMP not null comment '创建日期',
    update_time          datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新日期',
    constraint uni_class_id
        unique (class_id, version)
)
    comment '专利分类(IPC/CPC)配置表';

