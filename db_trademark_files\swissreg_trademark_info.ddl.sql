-- auto-generated definition
create table swissreg_trademark_basic_info
(
    id                       bigint auto_increment
        primary key,
    open_id                  varchar(50)  default ''                not null comment '数据源ID',
    trademark_id             varchar(50)  default ''                not null comment '商标ID',
    publication_date         varchar(255) default ''                not null comment '发布日期',
    trademark_img            varchar(255) default ''                not null comment '商标img',
    application_id           varchar(50)  default ''                not null comment '申请ID',
    trademark_category       varchar(255) default ''                not null comment '商标类',
    trademark_type           varchar(255) default ''                not null comment '商标类型',
    filing_date              varchar(255) default ''                not null comment '提交日期',
    registration_date        varchar(255) default ''                not null comment '注册日期',
    first_publication        varchar(255) default ''                not null comment '首次出版',
    protection_expiry_date   varchar(255) default ''                not null comment '保护到期日',
    cancellation             longtext                               null comment '撤销',
    priority_claim           longtext                               null comment '优先索赔',
    remarks                  longtext                               null comment '评论',
    renewed_trademark        varchar(255) default ''                not null comment '续展商标',
    colour_claim             longtext                               null comment '颜色主张',
    acquired_distinctiveness longtext                               null comment '独特性',
    create_time              datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time              datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uni_mark_id
        unique (trademark_id) comment '数据源ID唯一索引'
)
    comment '商标基础表';

create index idx_create_time
    on swissreg_trademark_basic_info (create_time);

create index idx_open_id
    on swissreg_trademark_basic_info (open_id);

create index idx_utime
    on swissreg_trademark_basic_info (update_time);

