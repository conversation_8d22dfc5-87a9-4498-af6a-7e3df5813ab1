#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接配置文件
请根据实际情况修改数据库连接参数
"""

# 数据库连接配置
DATABASE_CONFIG = {
    "host": "*********",        # 数据库主机地址
    "port": 3306,              # 数据库端口
    "user": "yuxm",            # 数据库用户名
    "password": "G$jeuwCsk92E@7JZ^P",  # 数据库密码
    "database": "db_patent_staging",  # 数据库名称
    "charset": "utf8mb4"       # 字符集
}

# IPC数据配置
IPC_CONFIG = {
    "version": "2026.01",      # IPC版本
    "batch_size": 100,         # 批量插入大小
    "json_file": "ipc.json"    # JSON数据文件路径
}
