-- auto-generated definition
create table swissreg_trademark_addresses
(
    id                     bigint auto_increment
        primary key,
    open_id                varchar(50)  default ''                not null comment '数据源ID',
    trademark_id           varchar(50)                            not null comment '商标ID',
    address_id             varchar(255) default ''                not null,
    address_type           varchar(50)                            not null comment '地址类型（INHABER 或 VERTRETER）',
    name                   varchar(255)                           not null comment '名称',
    street                 varchar(255) default ''                not null comment '街道',
    zip_code               varchar(50)  default ''                not null comment '邮政编码',
    town                   varchar(255) default ''                not null comment '城市',
    country                varchar(255) default ''                not null comment '国家',
    house_number           varchar(50)  default ''                not null comment '门牌号',
    house_number_extension varchar(255) default ''                not null comment '门牌号附加信息',
    email                  varchar(255) default ''                not null comment '电子邮件',
    phone_number           varchar(50)  default ''                not null comment '电话号码',
    fax_number             varchar(50)  default ''                not null comment '传真号码',
    language               varchar(50)  default ''                not null comment '语言',
    modification           longtext                               null comment '修改信息',
    create_time            datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time            datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uni_mark_id
        unique (trademark_id, address_id) comment '商标ID唯一索引'
)
    comment '商标地址表';

create index idx_open_id
    on swissreg_trademark_addresses (open_id);

create index idx_utime
    on swissreg_trademark_addresses (update_time);

