-- auto-generated definition
create table wipo_mark_disclaimer_details_new
(
    id            int(10) auto_increment comment '自增id'
        primary key,
    st13          varchar(30) default ''                not null comment '品牌id唯一标识',
    text          longtext                              null comment '声明文本',
    language_code varchar(10) default ''                not null comment '语言代码',
    create_time   datetime    default CURRENT_TIMESTAMP not null comment '任务创建时间',
    update_time   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '任务更新时间',
    constraint uniq_id
        unique (st13)
)
    comment 'wipo品牌商标免责声明表';

create index idx_utime
    on wipo_mark_disclaimer_details_new (update_time);

