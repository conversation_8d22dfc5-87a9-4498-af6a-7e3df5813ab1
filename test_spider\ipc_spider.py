#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPC分类数据清洗和入库脚本
处理IPC分类JSON数据，转换为数据库格式并入库
"""

import json
import re
import pymysql
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging
from db_config import DATABASE_CONFIG, IPC_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class IPCClassification:
    """IPC分类数据结构"""
    class_id: str
    class_description_en: str
    class_type: str = "IPC"
    class_level_1: str = ""
    class_level_2: str = ""
    class_level_3: str = ""
    class_level_4: str = ""
    version: str = "2026.01"


class DatabaseConfig:
    """数据库连接配置"""
    def __init__(self):
        # 从配置文件加载数据库连接参数
        self.host = DATABASE_CONFIG["host"]
        self.port = DATABASE_CONFIG["port"]
        self.user = DATABASE_CONFIG["user"]
        self.password = DATABASE_CONFIG["password"]
        self.database = DATABASE_CONFIG["database"]
        self.charset = DATABASE_CONFIG["charset"]

    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(
            host=self.host,
            port=self.port,
            user=self.user,
            password=self.password,
            database=self.database,
            charset=self.charset,
            autocommit=False
        )


class IPCDataProcessor:
    """IPC数据处理器"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config

    def clean_html_tags(self, text: str) -> str:
        """清理HTML标签，提取纯文本"""
        if not text:
            return ""

        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', text)
        # 移除多余的空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        return clean_text

    def determine_class_levels(self, symbol_code: str) -> Dict[str, str]:
        """
        根据symbolcode确定分类层级
        IPC分类层级规则：
        - Section: A, B, C, D, E, F, G, H (1位字母)
        - Class: A01, B01, etc. (1位字母+2位数字)
        - Subclass: A01B, A01C, etc. (1位字母+2位数字+1位字母)
        - Main Group: A01B001, etc. (更长的编码)
        """
        levels = {
            "class_level_1": "",
            "class_level_2": "",
            "class_level_3": "",
            "class_level_4": ""
        }

        if not symbol_code:
            return levels

        # Section级别 (如: A)
        if len(symbol_code) == 1 and symbol_code.isalpha():
            levels["class_level_1"] = symbol_code

        # Class级别 (如: A01)
        elif len(symbol_code) == 3 and symbol_code[0].isalpha() and symbol_code[1:].isdigit():
            levels["class_level_1"] = symbol_code[0]
            levels["class_level_2"] = symbol_code

        # Subclass级别 (如: A01B)
        elif len(symbol_code) == 4 and symbol_code[0].isalpha() and symbol_code[1:3].isdigit() and symbol_code[3].isalpha():
            levels["class_level_1"] = symbol_code[0]
            levels["class_level_2"] = symbol_code[:3]
            levels["class_level_3"] = symbol_code

        # Main Group或更细分级别
        elif len(symbol_code) > 4:
            if symbol_code[0].isalpha() and len(symbol_code) >= 4:
                levels["class_level_1"] = symbol_code[0]
                if len(symbol_code) >= 3:
                    levels["class_level_2"] = symbol_code[:3]
                if len(symbol_code) >= 4:
                    levels["class_level_3"] = symbol_code[:4]
                if len(symbol_code) > 4:
                    levels["class_level_4"] = symbol_code

        return levels

    def process_ipc_node(self, node: Dict[str, Any]) -> Optional[IPCClassification]:
        """
        处理单个IPC节点，转换为IPCClassification对象
        """
        # 检查是否有symbolcode
        symbol_code = node.get('symbolcode', '').strip()
        if not symbol_code:
            return None

        # 获取描述信息
        title1 = node.get('title1', '')
        description = self.clean_html_tags(title1)

        if not description:
            return None

        # 确定分类层级
        levels = self.determine_class_levels(symbol_code)

        # 创建分类对象
        classification = IPCClassification(
            class_id=symbol_code,
            class_description_en=description,
            class_level_1=levels["class_level_1"],
            class_level_2=levels["class_level_2"],
            class_level_3=levels["class_level_3"],
            class_level_4=levels["class_level_4"]
        )

        return classification

    def extract_all_classifications(self, data: List[Dict[str, Any]]) -> List[IPCClassification]:
        """
        递归提取所有IPC分类数据
        """
        classifications = []

        def recursive_extract(nodes):
            if not isinstance(nodes, list):
                return

            for node in nodes:
                if not isinstance(node, dict):
                    continue

                # 处理当前节点
                classification = self.process_ipc_node(node)
                if classification:
                    classifications.append(classification)
                    logger.debug(f"提取分类: {classification.class_id} - {classification.class_description_en[:50]}...")

                # 递归处理子节点
                children = node.get('children')
                if children:
                    recursive_extract(children)

        recursive_extract(data)
        return classifications

    def load_ipc_data(self, json_file_path: str) -> List[IPCClassification]:
        """
        从JSON文件加载IPC数据
        """
        logger.info(f"开始加载IPC数据文件: {json_file_path}")

        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            classifications = self.extract_all_classifications(data)
            logger.info(f"成功提取 {len(classifications)} 条IPC分类数据")

            return classifications

        except Exception as e:
            logger.error(f"加载IPC数据失败: {e}")
            raise


class IPCDatabaseManager:
    """IPC数据库管理器"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config

    def create_connection_pool(self):
        """创建数据库连接池（简单实现）"""
        # 这里可以使用更复杂的连接池实现，如 DBUtils
        return self.db_config.get_connection()

    def insert_classification(self, conn, classification: IPCClassification) -> bool:
        """
        插入单条分类数据
        """
        sql = """
        INSERT INTO patent_class_map (
            class_type, class_id, class_level_1, class_level_2,
            class_level_3, class_level_4, class_description_en,
            class_description_cn, version
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) ON DUPLICATE KEY UPDATE
            class_level_1 = VALUES(class_level_1),
            class_level_2 = VALUES(class_level_2),
            class_level_3 = VALUES(class_level_3),
            class_level_4 = VALUES(class_level_4),
            class_description_en = VALUES(class_description_en),
            update_time = CURRENT_TIMESTAMP
        """

        try:
            with conn.cursor() as cursor:
                cursor.execute(sql, (
                    classification.class_type,
                    classification.class_id,
                    classification.class_level_1,
                    classification.class_level_2,
                    classification.class_level_3,
                    classification.class_level_4,
                    classification.class_description_en,
                    "",  # class_description_cn 暂时为空
                    classification.version
                ))
            return True
        except Exception as e:
            logger.error(f"插入分类数据失败 {classification.class_id}: {e}")
            return False

    def batch_insert_classifications(self, classifications: List[IPCClassification], batch_size: int = 100) -> bool:
        """
        批量插入分类数据
        """
        logger.info(f"开始批量插入 {len(classifications)} 条分类数据，批次大小: {batch_size}")

        conn = None
        try:
            conn = self.create_connection_pool()

            success_count = 0
            total_count = len(classifications)

            for i in range(0, total_count, batch_size):
                batch = classifications[i:i + batch_size]

                # 开始事务
                conn.begin()

                batch_success = True
                for classification in batch:
                    if not self.insert_classification(conn, classification):
                        batch_success = False
                        break

                if batch_success:
                    conn.commit()
                    success_count += len(batch)
                    logger.info(f"批次 {i//batch_size + 1} 插入成功，已处理: {min(i + batch_size, total_count)}/{total_count}")
                else:
                    conn.rollback()
                    logger.error(f"批次 {i//batch_size + 1} 插入失败，回滚事务")

            logger.info(f"批量插入完成，成功: {success_count}/{total_count}")
            return success_count == total_count

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"批量插入过程中发生错误: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def clear_ipc_data(self, version: str = "2026.01") -> bool:
        """
        清空指定版本的IPC数据
        """
        conn = None
        try:
            conn = self.create_connection_pool()

            sql = "DELETE FROM patent_class_map WHERE class_type = 'IPC' AND version = %s"

            with conn.cursor() as cursor:
                result = cursor.execute(sql, (version,))
                conn.commit()

            logger.info(f"清空IPC数据完成，删除 {result} 条记录")
            return True

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"清空IPC数据失败: {e}")
            return False
        finally:
            if conn:
                conn.close()


def main():
    """
    主函数 - IPC数据清洗和入库
    """
    # 配置数据库连接
    db_config = DatabaseConfig()

    # 显示配置信息
    print("=" * 60)
    print("IPC分类数据清洗和入库工具")
    print("=" * 60)
    print("当前配置:")
    print(f"  数据库: {db_config.host}:{db_config.port}/{db_config.database}")
    print(f"  IPC版本: {IPC_CONFIG['version']}")
    print(f"  批次大小: {IPC_CONFIG['batch_size']}")
    print(f"  数据文件: {IPC_CONFIG['json_file']}")
    print("如需修改配置，请编辑 db_config.py 文件")
    print("=" * 60)

    # 初始化处理器
    processor = IPCDataProcessor(db_config)
    db_manager = IPCDatabaseManager(db_config)

    # JSON文件路径
    json_file_path = IPC_CONFIG['json_file']

    try:
        # 1. 加载和处理数据
        logger.info("步骤1: 加载IPC数据...")
        classifications = processor.load_ipc_data(json_file_path)

        if not classifications:
            logger.warning("没有找到有效的IPC分类数据")
            return

        # 2. 显示数据统计
        logger.info("步骤2: 数据统计...")
        level_stats = {}
        for cls in classifications:
            if cls.class_level_4:
                level = "Level 4"
            elif cls.class_level_3:
                level = "Level 3"
            elif cls.class_level_2:
                level = "Level 2"
            elif cls.class_level_1:
                level = "Level 1"
            else:
                level = "Unknown"

            level_stats[level] = level_stats.get(level, 0) + 1

        print("\n数据统计:")
        for level, count in sorted(level_stats.items()):
            print(f"  {level}: {count} 条")

        # 3. 询问是否清空现有数据
        response = input("\n是否清空现有IPC数据? (y/N): ").strip().lower()
        if response == 'y':
            logger.info("步骤3: 清空现有数据...")
            db_manager.clear_ipc_data(IPC_CONFIG['version'])

        # 4. 批量插入数据
        logger.info("步骤4: 批量插入数据...")
        success = db_manager.batch_insert_classifications(
            classifications,
            batch_size=IPC_CONFIG['batch_size']
        )

        if success:
            logger.info("✅ IPC数据清洗和入库完成!")
        else:
            logger.error("❌ IPC数据入库过程中出现错误")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()